"""
测试连接脚本

用于测试与大华视频云平台的网络连接。
"""

import sys
import os
import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from dahua_sdk.util.base_user_info import BaseUserInfo


def test_connection():
    """测试连接"""
    print("=== 大华视频云平台连接测试 ===")
    
    # 加载配置
    BaseUserInfo._load_config()
    
    ip = BaseUserInfo.ip
    port = BaseUserInfo.port
    
    print(f"服务器地址: {ip}")
    print(f"服务器端口: {port}")
    
    # 测试HTTP连接
    print("\n--- 测试HTTP连接 ---")
    try:
        url = f"http://{ip}:{port}/videoService/accounts/authorize"
        response = requests.get(url, timeout=10, verify=False)
        print(f"HTTP连接状态码: {response.status_code}")
        print(f"HTTP响应内容: {response.text[:200]}...")
    except Exception as e:
        print(f"HTTP连接失败: {e}")
    
    # 测试HTTPS连接
    print("\n--- 测试HTTPS连接 ---")
    try:
        url = f"https://{ip}:{port}/videoService/accounts/authorize"
        response = requests.get(url, timeout=10, verify=False)
        print(f"HTTPS连接状态码: {response.status_code}")
        print(f"HTTPS响应内容: {response.text[:200]}...")
    except Exception as e:
        print(f"HTTPS连接失败: {e}")
    
    # 测试其他常用端口
    print("\n--- 测试其他常用端口 ---")
    ports = [7282, 7283, 443, 80, 8080, 8443]
    
    for test_port in ports:
        if test_port == port:
            continue
            
        print(f"\n测试端口 {test_port}:")
        
        # HTTP
        try:
            url = f"http://{ip}:{test_port}/videoService/accounts/authorize"
            response = requests.get(url, timeout=5, verify=False)
            print(f"  HTTP {test_port}: 状态码 {response.status_code}")
        except Exception as e:
            print(f"  HTTP {test_port}: 失败 - {type(e).__name__}")
        
        # HTTPS
        try:
            url = f"https://{ip}:{test_port}/videoService/accounts/authorize"
            response = requests.get(url, timeout=5, verify=False)
            print(f"  HTTPS {test_port}: 状态码 {response.status_code}")
        except Exception as e:
            print(f"  HTTPS {test_port}: 失败 - {type(e).__name__}")


if __name__ == "__main__":
    test_connection()
