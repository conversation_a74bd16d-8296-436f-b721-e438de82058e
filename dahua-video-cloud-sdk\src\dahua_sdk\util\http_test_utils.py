"""
HTTP请求工具类

提供统一的HTTP请求方法，支持GET、POST、PUT、DELETE等操作。
"""

import requests
import json
from typing import Optional
from .http_enum import HttpEnum
from .base_user_info import BaseUserInfo


class HttpTestUtils:
    """HTTP请求工具类"""
    
    @staticmethod
    def http_request(method: HttpEnum, ip: str, port: int, action: str, 
                    token: str, content: str = "") -> Optional[str]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法枚举
            ip: 服务器IP地址
            port: 服务器端口
            action: API路径
            token: 认证token
            content: 请求内容（GET/DELETE为URL参数，POST/PUT为JSON体）
            
        Returns:
            响应JSON字符串，失败返回None
        """
        try:
            # 构建URL，根据实际服务器配置选择协议
            # 注意：原Java版本使用HTTP，但服务器可能已升级为HTTPS
            protocol = "https" if port in [7282, 7283, 443, 8443] else "http"
            uri = f"{protocol}://{ip}:{port}{action}"
            
            # 设置请求头
            headers = {
                "Content-Type": "application/json",
                "X-Subject-Token": token
            }

            # 禁用SSL验证（适用于内网自签名证书）
            verify_ssl = False
            
            # 根据HTTP方法发送请求
            response = None
            
            if method == HttpEnum.GET:
                url = uri + content if content else uri
                response = requests.get(url, headers=headers, timeout=30, verify=verify_ssl)

            elif method == HttpEnum.POST:
                response = requests.post(uri, data=content, headers=headers, timeout=30, verify=verify_ssl)

            elif method == HttpEnum.PUT:
                response = requests.put(uri, data=content, headers=headers, timeout=30, verify=verify_ssl)

            elif method == HttpEnum.DELETE:
                url = uri + content if content else uri
                response = requests.delete(url, headers=headers, timeout=30, verify=verify_ssl)
                
            else:
                print("请求方法不对")
                return None
            
            # 检查响应状态
            if response.status_code == 200:
                return response.text
            else:
                print(f"HTTP请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return response.text
                
        except requests.exceptions.Timeout:
            print("请求超时")
            return None
        except requests.exceptions.ConnectionError:
            print("连接错误")
            return None
        except Exception as e:
            print(f"请求异常: {e}")
            return None
    
    @staticmethod
    def get_token(ip: str, port: int, username: str, password: str) -> str:
        """
        获取登录token
        
        Args:
            ip: 服务器IP
            port: 服务器端口  
            username: 用户名
            password: 密码
            
        Returns:
            token字符串
            
        Raises:
            Exception: 获取token失败时抛出异常
        """
        # 避免循环导入，在方法内导入
        from ..login.login import Login
        
        try:
            response = Login.login(ip, port, username, password)
            if not response:
                raise Exception("登录响应为空")
                
            rsp = json.loads(response)
            
            # 检查是否有错误消息
            message = rsp.get("message")
            if message:
                print(message)
                raise Exception("未获取到token")
            
            # 获取token
            token = rsp.get("token")
            if not token:
                print("获取到的token为空")
                raise Exception("获取到的token为空")
                
            return token
            
        except json.JSONDecodeError:
            print("响应JSON解析失败")
            raise Exception("响应格式错误")
        except Exception as e:
            print(f"获取token失败: {e}")
            raise
