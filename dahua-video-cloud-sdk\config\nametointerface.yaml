# 接口名称到URL映射配置
# 从Java properties文件转换而来

# 会话管理
sessions: "/videoService/accounts/authorize"
keepalive: "/videoService/accounts/session/keepalive"
logout: "/videoService/accounts/session"

# 基础功能
orgtree: "/videoService/orgs/tree"
devinfo: "/videoService/devices"
devinfo_by_id: "/videoService/devices/{deviceId}"
devinfo_by_org: "/videoService/orgs/{orgId}/devices"

# 视频功能
video_url: "/videoService/devices/{deviceId}/channels/{channelId}/video/url"
video_url_by_time: "/videoService/devices/{deviceId}/channels/{channelId}/video/url/time"
video_url_by_file: "/videoService/devices/{deviceId}/channels/{channelId}/video/url/file"
video_download: "/videoService/devices/{deviceId}/channels/{channelId}/video/download"
video_download_by_time: "/videoService/devices/{deviceId}/channels/{channelId}/video/download/time"
video_download_by_file: "/videoService/devices/{deviceId}/channels/{channelId}/video/download/file"

# 预置点功能
preset_get: "/videoService/devices/{deviceId}/channels/{channelId}/ptz/preset"
preset_goto: "/videoService/devices/{deviceId}/channels/{channelId}/ptz/preset/{presetId}/goto"
preset_set: "/videoService/devices/{deviceId}/channels/{channelId}/ptz/preset/{presetId}"
preset_remove: "/videoService/devices/{deviceId}/channels/{channelId}/ptz/preset/{presetId}"

# 订阅功能
subscribe_alarm: "/videoService/devices/{deviceId}/channels/{channelId}/alarms/subscribe"
subscribe_alarm_by_org: "/videoService/orgs/{orgId}/alarms/subscribe"
unsubscribe_alarm: "/videoService/devices/{deviceId}/channels/{channelId}/alarms/unsubscribe"
unsubscribe_alarm_by_org: "/videoService/orgs/{orgId}/alarms/unsubscribe"

# 车辆智能功能
vehicle_passing: "/videoService/traffic/vehicles/passing"
vehicle_passing_by_time: "/videoService/traffic/vehicles/passing/time"
vehicle_passing_by_device: "/videoService/devices/{deviceId}/channels/{channelId}/traffic/vehicles/passing"
vehicle_passing_by_device_time: "/videoService/devices/{deviceId}/channels/{channelId}/traffic/vehicles/passing/time"

# 人像识别功能
face_detection: "/videoService/faces/detection"
face_detection_by_time: "/videoService/faces/detection/time"
face_detection_by_device: "/videoService/devices/{deviceId}/channels/{channelId}/faces/detection"
face_detection_by_device_time: "/videoService/devices/{deviceId}/channels/{channelId}/faces/detection/time"

# 其他功能接口
device_status: "/videoService/devices/{deviceId}/status"
channel_status: "/videoService/devices/{deviceId}/channels/{channelId}/status"
ptz_control: "/videoService/devices/{deviceId}/channels/{channelId}/ptz/control"
ptz_stop: "/videoService/devices/{deviceId}/channels/{channelId}/ptz/stop"

# 录像查询
record_query: "/videoService/devices/{deviceId}/channels/{channelId}/records"
record_query_by_time: "/videoService/devices/{deviceId}/channels/{channelId}/records/time"

# 图片抓拍
snapshot: "/videoService/devices/{deviceId}/channels/{channelId}/snapshot"
snapshot_download: "/videoService/devices/{deviceId}/channels/{channelId}/snapshot/download"

# 告警相关
alarm_query: "/videoService/alarms"
alarm_query_by_device: "/videoService/devices/{deviceId}/alarms"
alarm_query_by_org: "/videoService/orgs/{orgId}/alarms"

# 存储相关
storage_info: "/videoService/devices/{deviceId}/storage"
storage_format: "/videoService/devices/{deviceId}/storage/format"

# 系统信息
system_info: "/videoService/system/info"
system_time: "/videoService/system/time"
