# 大华视频云开放平台 Python SDK

这是一个完整的大华视频云平台 Python SDK 演示程序，从 Java SDK 转换而来，提供了登录认证、基础视频功能、车辆智能、人像识别等功能模块。

## 功能特性

- ✅ **登录认证**: 支持两阶段 MD5 加密登录、保活、登出
- ✅ **基础功能**: 组织管理、设备管理、视频功能、预置点、订阅
- ✅ **视频功能**: 实时视频、录像回放、视频下载
- ✅ **车辆智能**: 车辆过车记录查询
- ✅ **人像识别**: 人脸检测功能
- ✅ **配置管理**: YAML 配置文件支持
- ✅ **自动保活**: 110 秒间隔自动保活机制

## 项目结构

```
dahua-video-cloud-sdk/
├── src/dahua_sdk/          # 主要源码
│   ├── util/               # 核心工具类
│   │   ├── base_user_info.py    # 基础用户信息管理
│   │   ├── http_test_utils.py   # HTTP请求工具
│   │   └── http_enum.py         # HTTP方法枚举
│   ├── login/              # 登录认证模块
│   │   ├── login.py             # 登录接口
│   │   ├── keep_login.py        # 保活接口
│   │   ├── login_out.py         # 登出接口
│   │   └── login_beans.py       # 登录数据类
│   ├── basic/              # 基础功能模块
│   │   ├── org_tree.py          # 组织树接口
│   │   ├── dev_info.py          # 设备信息接口
│   │   ├── video/               # 视频功能
│   │   ├── preset/              # 预置点功能
│   │   └── subscribe/           # 订阅功能
│   ├── vehicle/            # 车辆智能模块
│   └── face_service/       # 人像识别模块
├── config/                 # 配置文件
│   ├── baseinfo.yaml            # 基础连接配置
│   └── nametointerface.yaml     # 接口映射配置
├── examples/               # 使用示例
│   └── basic_usage.py           # 基础使用示例
├── tests/                  # 测试文件
└── docs/                   # 文档
```

## 快速开始

### 1. 安装依赖

```bash
# 使用uv（推荐）
uv sync

# 或使用pip
pip install -r requirements.txt
```

### 2. 配置连接信息

编辑 `config/baseinfo.yaml` 文件：

```yaml
# 服务器IP地址
ip: "**************"

# 服务器端口
port: 7282

# 用户名
username: "your_username"

# 密码
password: "your_password"
```

### 3. 运行示例

```bash
# 运行基础使用示例
python examples/basic_usage.py

# 或者单独测试各个模块
python -m dahua_sdk.login.login
python -m dahua_sdk.basic.org_tree
python -m dahua_sdk.basic.dev_info
```

## 使用方法

### 基础使用

```python
import sys
import os
sys.path.insert(0, 'src')

from dahua_sdk.login.login import Login
from dahua_sdk.basic.org_tree import OrgTree
from dahua_sdk.util.base_user_info import BaseUserInfo

# 1. 登录获取token
response = Login.login("*************", 7282, "username", "password")
token = json.loads(response)["token"]
BaseUserInfo.save_token(token)

# 2. 获取组织树
org_response = OrgTree.get_org_tree("*************", 7282, token)
print(org_response)

# 3. 登出
LoginOut.login_out("*************", 7282, token)
```

### 自动保活

```python
import threading
import time
from dahua_sdk.login.keep_login import KeepLogin

def keep_alive_worker():
    while True:
        time.sleep(110)  # 每110秒保活一次
        KeepLogin.keep_login(ip, port, token)

# 启动保活线程
thread = threading.Thread(target=keep_alive_worker)
thread.daemon = True
thread.start()
```

## API 接口说明

### 登录认证

- `Login.login(ip, port, username, password)` - 完整登录流程
- `KeepLogin.keep_login(ip, port, token)` - 保活接口
- `LoginOut.login_out(ip, port, token)` - 登出接口

### 基础功能

- `OrgTree.get_org_tree(ip, port, token)` - 获取组织树
- `DevInfo.get_all_devices(ip, port, token)` - 获取所有设备
- `DevInfo.get_device_by_id(ip, port, token, device_id)` - 按 ID 获取设备

### 视频功能

- `VideoUrl.get_video_url(ip, port, token, device_id, channel_id)` - 获取实时视频 URL
- `VideoUrlByTime.get_video_url_by_time(...)` - 按时间获取录像 URL
- `VideoDownload.download_video(...)` - 下载视频文件

## 开发说明

### 技术栈

- **Python 3.9+**: 主要开发语言
- **requests**: HTTP 请求库
- **PyYAML**: YAML 配置文件解析
- **hashlib**: MD5 加密
- **threading**: 多线程保活

### 代码规范

- 使用类型提示（Type Hints）
- 遵循 PEP 8 代码规范
- 详细的文档字符串
- 异常处理和错误日志

### 测试

```bash
# 运行测试
pytest tests/

# 代码格式化
black src/

# 代码检查
flake8 src/
```

## 注意事项

1. **登录保活**: 登录后必须每 110 秒调用一次保活接口，否则 token 会在 2 分钟后过期
2. **配置安全**: 请妥善保管配置文件中的用户名和密码
3. **网络连接**: 确保网络能够访问大华视频云平台服务器
4. **设备 ID**: 调用设备相关接口时需要提供正确的设备 ID 和通道 ID

## 许可证

本项目基于原大华视频云 Java SDK 转换而来，仅供学习和演示使用。

## 更新日志

### v0.1.0 (2025-07-02)

- 初始版本发布
- 完成 Java SDK 到 Python 的完整转换
- 实现登录认证、基础功能、视频功能等核心模块
- 提供完整的使用示例和文档
