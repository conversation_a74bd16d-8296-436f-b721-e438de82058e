"""
设备信息接口

获取设备信息，支持按设备ID、组织ID等方式查询。
"""

import json
from typing import Optional
from ..util.base_user_info import BaseUserInfo
from ..util.http_enum import HttpEnum
from ..util.http_test_utils import HttpTestUtils


class DevInfo(BaseUserInfo):
    """设备信息接口类"""
    
    ACTION = "/videoService/devices"
    ACTION_BY_ID = "/videoService/devices/{deviceId}"
    ACTION_BY_ORG = "/videoService/orgs/{orgId}/devices"
    
    @staticmethod
    def get_all_devices(ip: str, port: int, token: str) -> Optional[str]:
        """
        获取所有设备信息
        
        Args:
            ip: 服务器IP
            port: 服务器端口
            token: 认证token
            
        Returns:
            设备信息JSON字符串
        """
        response = HttpTestUtils.http_request(
            HttpEnum.GET, ip, port, DevInfo.ACTION, token, ""
        )
        return response
    
    @staticmethod
    def get_device_by_id(ip: str, port: int, token: str, device_id: str) -> Optional[str]:
        """
        根据设备ID获取设备信息
        
        Args:
            ip: 服务器IP
            port: 服务器端口
            token: 认证token
            device_id: 设备ID
            
        Returns:
            设备信息JSON字符串
        """
        action = DevInfo.ACTION_BY_ID.replace("{deviceId}", device_id)
        response = HttpTestUtils.http_request(
            HttpEnum.GET, ip, port, action, token, ""
        )
        return response
    
    @staticmethod
    def get_devices_by_org(ip: str, port: int, token: str, org_id: str) -> Optional[str]:
        """
        根据组织ID获取设备信息
        
        Args:
            ip: 服务器IP
            port: 服务器端口
            token: 认证token
            org_id: 组织ID
            
        Returns:
            设备信息JSON字符串
        """
        action = DevInfo.ACTION_BY_ORG.replace("{orgId}", org_id)
        response = HttpTestUtils.http_request(
            HttpEnum.GET, ip, port, action, token, ""
        )
        return response
    
    @staticmethod
    def main():
        """
        主方法，用于测试设备信息功能
        
        获取设备信息接口，调用该接口前需要先登录获取token
        """
        try:
            # 检查是否已登录
            if not BaseUserInfo.token:
                print("请先登录获取token")
                return
            
            # 获取所有设备信息
            print("=== 获取所有设备信息 ===")
            response = DevInfo.get_all_devices(
                BaseUserInfo.ip,
                BaseUserInfo.port,
                BaseUserInfo.token
            )
            
            if response:
                try:
                    response_map = json.loads(response)
                    message = response_map.get("message")
                    
                    if message:
                        print(f"获取设备信息失败: {message}")
                    else:
                        print("获取设备信息成功:")
                        devices = response_map.get("devices", [])
                        print(f"共找到 {len(devices)} 个设备")
                        
                        # 显示前几个设备的基本信息
                        for i, device in enumerate(devices[:3]):
                            print(f"设备 {i+1}:")
                            print(f"  ID: {device.get('deviceId', 'N/A')}")
                            print(f"  名称: {device.get('deviceName', 'N/A')}")
                            print(f"  类型: {device.get('deviceType', 'N/A')}")
                            print(f"  状态: {device.get('status', 'N/A')}")
                        
                        if len(devices) > 3:
                            print(f"  ... 还有 {len(devices) - 3} 个设备")
                        
                        # 如果有设备，测试按ID查询
                        if devices:
                            device_id = devices[0].get('deviceId')
                            if device_id:
                                print(f"\n=== 按设备ID查询: {device_id} ===")
                                device_response = DevInfo.get_device_by_id(
                                    BaseUserInfo.ip,
                                    BaseUserInfo.port,
                                    BaseUserInfo.token,
                                    device_id
                                )
                                if device_response:
                                    device_data = json.loads(device_response)
                                    print("设备详细信息:")
                                    print(json.dumps(device_data, indent=2, ensure_ascii=False))
                        
                except json.JSONDecodeError:
                    print("设备信息响应JSON解析失败")
                    print(f"原始响应: {response}")
            else:
                print("获取设备信息请求失败")
                
        except Exception as e:
            print(f"获取设备信息异常: {e}")


if __name__ == "__main__":
    DevInfo.main()
