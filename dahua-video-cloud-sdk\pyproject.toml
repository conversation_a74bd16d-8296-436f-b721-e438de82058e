[project]
name = "dahua-video-cloud-sdk"
version = "0.1.0"
description = "大华视频云开放平台Python SDK演示程序"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "requests>=2.31.0",
    "pyyaml>=6.0",
    "configparser>=5.3.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/dahua_sdk"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
