import java.util.ResourceBundle;

public class SimpleTest {
    public static void main(String[] args) {
        System.out.println("开始简单测试...");
        
        try {
            // 测试读取baseinfo.properties
            System.out.println("尝试读取baseinfo.properties...");
            ResourceBundle bundle = ResourceBundle.getBundle("baseinfo");
            String ip = bundle.getString("ip");
            String port = bundle.getString("port");
            String userName = bundle.getString("userName");
            String password = bundle.getString("password");
            
            System.out.println("IP: " + ip);
            System.out.println("Port: " + port);
            System.out.println("UserName: " + userName);
            System.out.println("Password: " + password);
            
            // 测试读取token.properties
            System.out.println("尝试读取token.properties...");
            ResourceBundle tokenbundle = ResourceBundle.getBundle("token");
            String token = tokenbundle.getString("token");
            System.out.println("Token存在: " + (token != null && !token.isEmpty()));
            
            System.out.println("Properties文件读取成功！");
            
        } catch (Exception e) {
            System.out.println("错误: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试完成。");
    }
}
