"""
HTTP方法枚举类

定义了支持的HTTP请求方法。
"""

from enum import Enum


class HttpEnum(Enum):
    """HTTP请求方法枚举"""
    
    GET = (1, "get")
    POST = (2, "post") 
    PUT = (3, "put")
    DELETE = (4, "delete")
    
    def __init__(self, num, desc):
        self.num = num
        self.desc = desc
    
    def get_num(self):
        """获取方法编号"""
        return self.num
    
    def get_desc(self):
        """获取方法描述"""
        return self.desc
