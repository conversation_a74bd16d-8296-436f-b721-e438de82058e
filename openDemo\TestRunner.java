public class TestRunner {
    public static void main(String[] args) {
        System.out.println("测试开始...");
        
        try {
            // 测试BaseUserInfo类的加载
            Class.forName("com.dahua.demo.util.BaseUserInfo");
            System.out.println("BaseUserInfo类加载成功");
            
            // 测试Login类的加载
            Class.forName("com.dahua.demo.login.Login");
            System.out.println("Login类加载成功");
            
            System.out.println("所有基础类加载成功！");
            
        } catch (ClassNotFoundException e) {
            System.out.println("类加载失败: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.out.println("其他错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
