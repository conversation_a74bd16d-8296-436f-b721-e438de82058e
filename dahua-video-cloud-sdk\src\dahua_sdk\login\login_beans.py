"""
登录相关的数据类

包含第一次登录和第二次登录的参数类。
"""

import hashlib
from typing import Optional


class LoginFirst:
    """第一次登录参数类"""
    
    def __init__(self):
        self.user_name: Optional[str] = None
        self.client_type: Optional[str] = None
        self.ip_address: Optional[str] = None
    
    def set_user_name(self, user_name: str):
        """设置用户名"""
        self.user_name = user_name
    
    def get_user_name(self) -> Optional[str]:
        """获取用户名"""
        return self.user_name
    
    def set_client_type(self, client_type: str):
        """设置客户端类型"""
        self.client_type = client_type
    
    def get_client_type(self) -> Optional[str]:
        """获取客户端类型"""
        return self.client_type
    
    def set_ip_address(self, ip_address: str):
        """设置IP地址"""
        self.ip_address = ip_address
    
    def get_ip_address(self) -> Optional[str]:
        """获取IP地址"""
        return self.ip_address
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "userName": self.user_name,
            "clientType": self.client_type,
            "ipAddress": self.ip_address
        }
    
    def __str__(self) -> str:
        return f"[userName={self.user_name}, clientType={self.client_type}, ipAddress={self.ip_address}]"


class LoginSecond:
    """第二次登录参数类和方法"""
    
    def __init__(self):
        self.user_name: Optional[str] = None
        self.client_type: Optional[str] = None
        self.ip_address: Optional[str] = None
        self.signature: Optional[str] = None
        self.random_key: Optional[str] = None
        self.encrypt_type: Optional[str] = None
    
    def set_user_name(self, user_name: str):
        """设置用户名"""
        self.user_name = user_name
    
    def get_user_name(self) -> Optional[str]:
        """获取用户名"""
        return self.user_name
    
    def set_client_type(self, client_type: str):
        """设置客户端类型"""
        self.client_type = client_type
    
    def get_client_type(self) -> Optional[str]:
        """获取客户端类型"""
        return self.client_type
    
    def set_ip_address(self, ip_address: str):
        """设置IP地址"""
        self.ip_address = ip_address
    
    def get_ip_address(self) -> Optional[str]:
        """获取IP地址"""
        return self.ip_address
    
    def set_signature(self, signature: str):
        """设置签名"""
        self.signature = signature
    
    def get_signature(self) -> Optional[str]:
        """获取签名"""
        return self.signature
    
    def set_random_key(self, random_key: str):
        """设置随机密钥"""
        self.random_key = random_key
    
    def get_random_key(self) -> Optional[str]:
        """获取随机密钥"""
        return self.random_key
    
    def set_encrypt_type(self, encrypt_type: str):
        """设置加密类型"""
        self.encrypt_type = encrypt_type
    
    def get_encrypt_type(self) -> Optional[str]:
        """获取加密类型"""
        return self.encrypt_type
    
    @staticmethod
    def is_blank_string(s: Optional[str]) -> bool:
        """检查字符串是否为空"""
        return s is None or s == "" or len(s) == 0
    
    @staticmethod
    def encrypt(input_text: str, algorithm_name: str = "MD5") -> str:
        """
        MD5加密
        
        Args:
            input_text: 输入文本
            algorithm_name: 算法名称，默认MD5
            
        Returns:
            加密后的十六进制字符串
            
        Raises:
            ValueError: 输入文本为空时抛出异常
        """
        if LoginSecond.is_blank_string(input_text):
            raise ValueError("Please enter inputText!")
        
        if LoginSecond.is_blank_string(algorithm_name) or algorithm_name.lower() == "md5":
            algorithm_name = "MD5"
        
        # 使用hashlib进行MD5加密
        md5_hash = hashlib.md5()
        md5_hash.update(input_text.encode('utf-8'))
        return md5_hash.hexdigest()
    
    def calc_signature(self, password: str, realm: str) -> str:
        """
        根据第一次登录接口调用返回的参数，计算签名
        第二次登录接口调用要使用到，本例采用的是MD5的加密方式
        
        Args:
            password: 密码
            realm: 域
            
        Returns:
            计算出的签名
        """
        signature = self.encrypt(password, "MD5")
        signature = self.encrypt(self.user_name + signature, "MD5")
        signature = self.encrypt(signature, "MD5")
        signature = self.encrypt(f"{self.user_name}:{realm}:{signature}", "MD5")
        signature = self.encrypt(f"{signature}:{self.random_key}", "MD5")
        return signature
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "userName": self.user_name,
            "clientType": self.client_type,
            "ipAddress": self.ip_address,
            "signature": self.signature,
            "randomKey": self.random_key,
            "encryptType": self.encrypt_type
        }
