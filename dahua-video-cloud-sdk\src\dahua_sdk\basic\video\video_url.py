"""
实时视频URL接口

获取设备通道的实时视频流URL。
"""

import json
from typing import Optional
from ...util.base_user_info import BaseUserInfo
from ...util.http_enum import HttpEnum
from ...util.http_test_utils import HttpTestUtils


class VideoUrl(BaseUserInfo):
    """实时视频URL接口类"""
    
    ACTION = "/videoService/devices/{deviceId}/channels/{channelId}/video/url"
    
    @staticmethod
    def get_video_url(ip: str, port: int, token: str, device_id: str, 
                     channel_id: str, stream_type: str = "main") -> Optional[str]:
        """
        获取实时视频URL
        
        Args:
            ip: 服务器IP
            port: 服务器端口
            token: 认证token
            device_id: 设备ID
            channel_id: 通道ID
            stream_type: 码流类型，main(主码流)/sub(子码流)，默认main
            
        Returns:
            视频URL响应JSON字符串
        """
        action = VideoUrl.ACTION.replace("{deviceId}", device_id).replace("{channelId}", channel_id)
        
        # 构建查询参数
        params = f"?streamType={stream_type}"
        
        response = HttpTestUtils.http_request(
            HttpEnum.GET, ip, port, action, token, params
        )
        return response
    
    @staticmethod
    def main():
        """
        主方法，用于测试实时视频URL功能
        
        获取实时视频URL接口，调用该接口前需要先登录获取token
        需要提供有效的设备ID和通道ID
        """
        try:
            # 检查是否已登录
            if not BaseUserInfo.token:
                print("请先登录获取token")
                return
            
            # 示例设备ID和通道ID（需要根据实际环境修改）
            device_id = "your_device_id"  # 请替换为实际的设备ID
            channel_id = "1"  # 通道ID，通常从1开始
            
            print(f"=== 获取实时视频URL ===")
            print(f"设备ID: {device_id}")
            print(f"通道ID: {channel_id}")
            
            # 获取主码流URL
            print("\n--- 主码流 ---")
            response = VideoUrl.get_video_url(
                BaseUserInfo.ip,
                BaseUserInfo.port,
                BaseUserInfo.token,
                device_id,
                channel_id,
                "main"
            )
            
            if response:
                try:
                    response_map = json.loads(response)
                    message = response_map.get("message")
                    
                    if message:
                        print(f"获取主码流URL失败: {message}")
                    else:
                        url = response_map.get("url")
                        if url:
                            print(f"主码流URL: {url}")
                        else:
                            print("响应中未找到URL")
                        
                except json.JSONDecodeError:
                    print("主码流响应JSON解析失败")
                    print(f"原始响应: {response}")
            else:
                print("获取主码流URL请求失败")
            
            # 获取子码流URL
            print("\n--- 子码流 ---")
            response = VideoUrl.get_video_url(
                BaseUserInfo.ip,
                BaseUserInfo.port,
                BaseUserInfo.token,
                device_id,
                channel_id,
                "sub"
            )
            
            if response:
                try:
                    response_map = json.loads(response)
                    message = response_map.get("message")
                    
                    if message:
                        print(f"获取子码流URL失败: {message}")
                    else:
                        url = response_map.get("url")
                        if url:
                            print(f"子码流URL: {url}")
                        else:
                            print("响应中未找到URL")
                        
                except json.JSONDecodeError:
                    print("子码流响应JSON解析失败")
                    print(f"原始响应: {response}")
            else:
                print("获取子码流URL请求失败")
                
        except Exception as e:
            print(f"获取视频URL异常: {e}")


if __name__ == "__main__":
    VideoUrl.main()
