"""
创建会话接口

实现两阶段登录认证机制。
"""

import json
from typing import Optional
from ..util.base_user_info import BaseUserInfo
from ..util.http_enum import HttpEnum
from ..util.http_test_utils import HttpTestUtils
from .login_beans import LoginFirst, LoginSecond


class Login(BaseUserInfo):
    """创建会话接口类"""
    
    ACTION = "/videoService/accounts/authorize"
    
    @staticmethod
    def first_login(ip: str, port: int, user_name: str) -> Optional[str]:
        """
        第一次登录，客户端只传用户名，服务端返回realm、randomKey和encryptType信息
        
        Args:
            ip: 服务器IP
            port: 服务器端口
            user_name: 用户名
            
        Returns:
            响应JSON字符串
        """
        login_first = LoginFirst()
        login_first.set_client_type("winpc")
        login_first.set_user_name(user_name)
        
        content = json.dumps(login_first.to_dict())
        response = HttpTestUtils.http_request(
            HttpEnum.POST, ip, port, Login.ACTION, "", content
        )
        return response
    
    @staticmethod
    def second_login(ip: str, port: int, user_name: str, password: str, 
                    realm: str, random_key: str) -> Optional[str]:
        """
        第二次登录，客户端根据返回的信息，按照指定的加密算法计算签名，
        再带着用户名和签名登录一次
        
        Args:
            ip: 服务器IP
            port: 服务器端口
            user_name: 用户名
            password: 密码
            realm: 域
            random_key: 随机密钥
            
        Returns:
            响应JSON字符串
        """
        snd = LoginSecond()
        snd.set_user_name(user_name)
        snd.set_client_type("winpc")
        snd.set_random_key(random_key)
        snd.set_encrypt_type("MD5")
        
        signature = snd.calc_signature(password, realm)
        snd.set_signature(signature)
        
        content = json.dumps(snd.to_dict())
        response = HttpTestUtils.http_request(
            HttpEnum.POST, ip, port, Login.ACTION, "", content
        )
        return response
    
    @staticmethod
    def login(ip: str, port: int, user_name: str, password: str) -> Optional[str]:
        """
        完整的登录流程
        
        Args:
            ip: 服务器IP
            port: 服务器端口
            user_name: 用户名
            password: 密码
            
        Returns:
            登录响应JSON字符串
            
        Raises:
            Exception: 登录失败时抛出异常
        """
        # 第一次登录
        response = Login.first_login(ip, port, user_name)
        if not response:
            raise Exception("第一次登录失败")
        
        try:
            response_map = json.loads(response)
            random_key = response_map.get("randomKey")
            realm = response_map.get("realm")
            
            if not random_key or not realm:
                raise Exception("第一次登录返回数据不完整")
            
            # 第二次登录
            response = Login.second_login(ip, port, user_name, password, realm, random_key)
            return response
            
        except json.JSONDecodeError:
            raise Exception("第一次登录响应JSON解析失败")
    
    @staticmethod
    def save_token_to_file(token: str):
        """保存token到配置文件"""
        BaseUserInfo.save_token(token)
    
    @staticmethod
    def main():
        """
        主方法，用于测试登录功能
        调用该登录方法之前需要修改 config/baseinfo.yaml中ip,port，username，password为当前对接环境
        登录的方法，运行之后即可获取到token，进行登录
        登录完成后，必须调用KeepLogin保活接口，否则，2分钟后会登录过期
        """
        try:
            # 获取token
            token = HttpTestUtils.get_token(
                BaseUserInfo.ip, 
                BaseUserInfo.port, 
                BaseUserInfo.username, 
                BaseUserInfo.password
            )
            
            # 保存token
            Login.save_token_to_file(token)
            print(f"登录成功，token={token}")
            
        except Exception as e:
            print(f"登录失败: {e}")


if __name__ == "__main__":
    Login.main()
