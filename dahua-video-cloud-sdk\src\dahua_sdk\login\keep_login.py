"""
保活接口

维持登录会话，防止token过期。
"""

import json
from typing import Optional
from ..util.base_user_info import BaseUserInfo
from ..util.http_enum import HttpEnum
from ..util.http_test_utils import HttpTestUtils


class KeepLogin(BaseUserInfo):
    """保活接口类"""
    
    ACTION = "/videoService/accounts/session/keepalive"
    
    @staticmethod
    def keep_login(ip: str, port: int, token: str) -> Optional[str]:
        """
        保活接口
        
        Args:
            ip: 服务器IP
            port: 服务器端口
            token: 认证token
            
        Returns:
            响应JSON字符串
        """
        response = HttpTestUtils.http_request(
            HttpEnum.PUT, ip, port, KeepLogin.ACTION, token, ""
        )
        return response
    
    @staticmethod
    def main():
        """
        主方法，用于测试保活功能
        
        保活接口，登录完成后，必须调用该接口，否则，2分钟后会登录过期
        建议每110秒调用一次该接口
        """
        try:
            # 检查是否已登录
            if not BaseUserInfo.token:
                print("请先登录获取token")
                return
            
            # 调用保活接口
            response = KeepLogin.keep_login(
                BaseUserInfo.ip,
                BaseUserInfo.port, 
                BaseUserInfo.token
            )
            
            if response:
                try:
                    response_map = json.loads(response)
                    message = response_map.get("message")
                    
                    if message:
                        print(f"保活失败: {message}")
                    else:
                        print("保活成功")
                        
                except json.JSONDecodeError:
                    print("保活响应JSON解析失败")
            else:
                print("保活请求失败")
                
        except Exception as e:
            print(f"保活异常: {e}")


if __name__ == "__main__":
    KeepLogin.main()
