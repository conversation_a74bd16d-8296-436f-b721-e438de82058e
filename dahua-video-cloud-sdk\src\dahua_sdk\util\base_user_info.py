"""
基础用户信息类

管理用户连接信息和token，从配置文件中读取基础配置。
"""

import os
import yaml
import configparser
from pathlib import Path


class BaseUserInfo:
    """基础用户信息管理类"""
    
    # 类变量，所有实例共享
    ip = None
    port = None
    username = None
    password = None
    token = None
    
    @classmethod
    def _load_config(cls):
        """加载配置文件"""
        if cls.ip is not None:  # 已经加载过配置
            return
            
        # 查找配置文件
        config_paths = [
            "config/baseinfo.yaml",
            "config/baseinfo.yml", 
            "../config/baseinfo.yaml",
            "../../config/baseinfo.yaml",
        ]
        
        config_file = None
        for path in config_paths:
            if os.path.exists(path):
                config_file = path
                break
                
        if not config_file:
            # 如果没有找到YAML配置文件，尝试创建默认配置
            cls._create_default_config()
            config_file = "config/baseinfo.yaml"
        
        # 读取YAML配置
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                
            cls.ip = config.get('ip', '**************')
            cls.port = config.get('port', 7282)
            cls.username = config.get('username', 'haishi')
            cls.password = config.get('password', 'haishi@123')
            
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            # 使用默认值
            cls.ip = '**************'
            cls.port = 7282
            cls.username = 'haishi'
            cls.password = 'haishi@123'
        
        # 读取token
        cls._load_token()
    
    @classmethod
    def _create_default_config(cls):
        """创建默认配置文件"""
        os.makedirs("config", exist_ok=True)
        
        default_config = {
            'ip': '**************',
            'port': 7282,
            'username': 'haishi',
            'password': 'haishi@123'
        }
        
        with open("config/baseinfo.yaml", 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
    
    @classmethod
    def _load_token(cls):
        """加载token"""
        token_paths = [
            "config/token.yaml",
            "../config/token.yaml", 
            "../../config/token.yaml",
        ]
        
        for path in token_paths:
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        token_config = yaml.safe_load(f)
                        cls.token = token_config.get('token', '')
                        if cls.token:
                            return
                except Exception:
                    continue
        
        # 如果没有找到token或token为空
        if not cls.token:
            print("没有进行登录，请调用Login进行登录")
            cls.token = ''
    
    @classmethod
    def save_token(cls, token):
        """保存token到配置文件"""
        cls.token = token
        os.makedirs("config", exist_ok=True)
        
        token_config = {'token': token}
        with open("config/token.yaml", 'w', encoding='utf-8') as f:
            yaml.dump(token_config, f, default_flow_style=False)
    
    @classmethod
    def clear_token(cls):
        """清除token"""
        cls.token = ''
        cls.save_token('')
    
    def __init__(self):
        """初始化时加载配置"""
        self._load_config()


# 在模块加载时自动加载配置
BaseUserInfo._load_config()
