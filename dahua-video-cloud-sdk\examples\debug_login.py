"""
调试登录接口

详细测试登录接口的各个步骤。
"""

import sys
import os
import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from dahua_sdk.util.base_user_info import BaseUserInfo


def debug_login():
    """调试登录过程"""
    print("=== 调试大华视频云登录接口 ===")
    
    # 加载配置
    BaseUserInfo._load_config()
    
    ip = BaseUserInfo.ip
    port = BaseUserInfo.port
    username = BaseUserInfo.username
    password = BaseUserInfo.password
    
    print(f"服务器地址: {ip}")
    print(f"服务器端口: {port}")
    print(f"用户名: {username}")
    print(f"密码: {password}")
    
    # 测试第一次登录接口
    print("\n--- 测试第一次登录接口 ---")
    first_login_url = f"https://{ip}:{port}/videoService/accounts/authorize"
    
    print(f"第一次登录URL: {first_login_url}")
    
    try:
        # 构建第一次登录请求体
        first_login_data = {
            "userName": username,
            "ipAddr": "",
            "clientType": ""
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        print(f"请求头: {headers}")
        print(f"请求体: {first_login_data}")
        
        response = requests.post(
            first_login_url, 
            json=first_login_data, 
            headers=headers, 
            timeout=10,
            verify=False
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("第一次登录请求成功！")
        else:
            print(f"第一次登录请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"第一次登录请求异常: {e}")
    
    # 测试其他可能的登录接口
    print("\n--- 测试其他可能的登录接口 ---")
    
    other_urls = [
        f"https://{ip}:{port}/videoService/accounts/login",
        f"https://{ip}:{port}/api/accounts/authorize",
        f"https://{ip}:{port}/api/accounts/login",
        f"https://{ip}:{port}/accounts/authorize",
        f"https://{ip}:{port}/accounts/login"
    ]
    
    for url in other_urls:
        print(f"\n测试URL: {url}")
        try:
            response = requests.post(
                url, 
                json={"userName": username}, 
                headers={"Content-Type": "application/json"}, 
                timeout=5,
                verify=False
            )
            print(f"  状态码: {response.status_code}")
            if response.status_code != 404:
                print(f"  响应: {response.text[:100]}...")
        except Exception as e:
            print(f"  异常: {type(e).__name__}")


if __name__ == "__main__":
    debug_login()
